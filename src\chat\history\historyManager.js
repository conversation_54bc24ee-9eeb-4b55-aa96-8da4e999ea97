/**
 * Message history management utilities
 * @module historyManager
 */

import { formatMessage } from '../utils/formatUtils.js';

/**
 * Formats Redis messages into chat history string
 * @param {Array<Object>} messages - Messages to format
 * @param {Object} env - Environment variables
 * @returns {string} Formatted message history
 */
export function formatRedisHistory(messages, env) {
	console.log('Formatting Redis history:', messages);
	return messages.map((message) => formatMessage(message, env)).join('\n');
}

/**
 * Handles reply-to message context
 * @param {Object} messageData - The message data containing reply information
 * @returns {string} Formatted reply context or empty string
 */
export function handleReplyContext(messageData) {
	if (!messageData.replyToMessage) return '';

	const { replyToMessage } = messageData;
	const replyText = replyToMessage.text || replyToMessage.caption;

	if (!replyText) return '';

	const contextType = replyToMessage.text ? 'message' : 'media with caption';
	return `\nCurrently replying to ${contextType}: "${replyText.trim()}"`;
}
